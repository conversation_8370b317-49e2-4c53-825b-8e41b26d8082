<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1200"
    @cancel="cancel"
    modalHeight="800"
  >
    <div slot="content" style="height: 100%">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        layout="horizontal"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 14 }"
      >
        <a-row class="form-row" :gutter="32">
          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">基本信息</div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="时间维度" prop="planType">
              <a-radio-group
                v-model="form.planType"
                :options="radioOptions"
                @change="handlePlanTypeChange"
                :disabled="isDetail || !!rowInfo?.planReportId"
              ></a-radio-group>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="计划时间" prop="dateRange">
              <a-range-picker
                v-if="form.planType !== 3"
                style="width: 100%"
                v-model="form.dateRange"
                :allowClear="true"
                :placeholder="['开始时间', '结束时间']"
                :format="'YYYY-MM-DD'"
                :mode="['date', 'date']"
                @panelChange="handlePanelChange"
                :disabledDate="disabledDate"
                @change="handleDateRangeChange"
                :disabled="isDetail || !!rowInfo?.planReportId"
              />
              <a-range-picker
                v-else
                style="width: 100%"
                v-model="form.dateRange"
                :allowClear="true"
                :placeholder="['开始月份', '结束月份']"
                format="YYYY-MM"
                :mode="['month', 'month']"
                @panelChange="handlePanelChange"
                :disabledDate="disabledMonthDate"
                @change="handleDateRangeChange"
                :disabled="isDetail || !!rowInfo?.planReportId"
              />
            </a-form-model-item>
          </a-col>

          <a-col :lg="24" :md="24" :sm="24" :span="24">
            <div class="title">用水计划</div>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24" :span="12">
            <a-form-model-item label="上报方式" prop="reportType">
              <a-radio-group
                v-model="form.reportType"
                :options="[
                  { label: '时段汇总', value: 1 },
                  { label: '直开口渠', value: 2 },
                ]"
                @change="handleReportTypeChange"
                :disabled="isDetail || !!rowInfo?.planReportId"
              ></a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>

      <div style="margin: 0 16px 5px">
        <span style="font-weight: 500">注：</span>
        <span>单位：Q日，1Q日=8.64万m³</span>
      </div>

      <vxe-grid
        v-if="!!columns.length"
        :key="columns.length"
        border="full"
        min-height="80"
        size="small"
        ref="vxeTableRef"
        :columnConfig="{ resizable: false }"
        :columns="columns"
        :data="tableData"
        :rowConfig="{ isHover: false }"
        :mergeCells="[{ row: 0, col: 0, rowspan: 9, colspan: 1 }]"
        show-footer
        :footerMethod="footerMethod"
        :merge-footer-items="[
          { row: 0, col: 0, rowspan: 1, colspan: 2 },
          { row: 1, col: 0, rowspan: 1, colspan: 2 },
          { row: 2, col: 0, rowspan: 1, colspan: 2 },
          { row: 2, col: 2, rowspan: 1, colspan: 999 },
        ]"
      />
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
      <a-button type="primary" @click="submitForm" :loading="loading" v-if="!isDetail">确定</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getOrgTree } from '@/api/user'
  import { addPlanReport, getPlanReportDetails, updatePlanReport, getPlanReportList } from '../services'
  import moment from 'moment'
  import AntModal from '@/components/pt/dialog/AntModal'
  import getMapFlatTree from '@/utils/getMapFlatTree.js'
  import { orgConfig } from '../config'
  import { dealNumber, getFixedNum } from '@/utils/dealNumber.js'
  import * as _ from 'lodash'

  export default {
    name: 'OfficeFormModal',
    components: { AntModal },
    props: ['radioOptions'],
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        formTitle: '',
        rowInfo: null,
        deptName: '',
        isDetail: false,
        columns: [],
        tableData: [],
        summaries: [],
        remarks: undefined,


        // 表单参数
        form: {
          planReportId: undefined,
          planType: 1,
          dateRange: undefined,
          reportType: 1,
          planName: '',
        },
        rules: {
          planType: [{ required: true, message: '时间维度不能为空', trigger: 'change' }],
          dateRange: [{ required: true, message: '计划时间不能为空', trigger: 'change' }],
          reportType: [{ required: true, message: '上报方式不能为空', trigger: 'change' }],
        },
      }
    },
    created() {},
    mounted() {},
    computed: {},
    watch: {},
    methods: {
      disabledMonthDate(current) {
        // 只能选择本月之后的月份
        return current && current < moment().add(1, 'month').startOf('month')
      },
      disabledDate(current) {
        return current && current > moment().endOf('day')
      },
      handleDateRangeChange() {
        setTimeout(() => {
          this.$nextTick(() => {
            this.getPlanInfo()
          })
        }, 20)
      },
      handlePanelChange(value, mode) {
        this.form.dateRange = value
        this.handleDateRangeChange()
      },
      handlePlanTypeChange(e) {
        if (e.target.value === 1) {
          this.form.dateRange = [moment().add(1, 'day'), moment().add(5, 'day')]
        }
        if (e.target.value === 2) {
          this.form.dateRange = [moment().add(1, 'day'), moment().add(10, 'day')]
        }
        if (e.target.value === 3) {
          // 默认选择下个月作为起始和结束月份
          this.form.dateRange = [moment().add(1, 'month').startOf('month'), moment().add(1, 'month').startOf('month')]
        }

        if (this.rowInfo) {
          this.form.planType = +this.rowInfo.planType
          this.form.dateRange = [moment(this.rowInfo.planStartDate), moment(this.rowInfo.planEndDate)]
        }

        this.form.planName =
          this.rowInfo?.planName ||
          `${this.deptName}${this.radioOptions.find(item => item.value === this.form.planType)?.label}计划${this.form.dateRange[0].format('YYYY-MM-DD')}`

        this.$nextTick(() => {
          this.getPlanInfo()
        })
      },

      handleReportTypeChange() {
        this.$nextTick(() => {
          this.getPlanInfo()
        })
      },

      sumNum2(list, date, field) {
        let count = 0
        list.forEach((item, idx) => {
          count += Number(item.recordObj[date][field])
        })
        return getFixedNum(count, 1)
      },
      footerMethod({ columns, data }) {
        const footerData = [
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return this.deptName + '合计'
            }
            if (_columnIndex > 1 && _columnIndex < columns.length - 1) {
              return `${this.sumNum2(data, column.property, 'planStartFlow')} ~ ${this.sumNum2(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return this.deptName + '上报'
            }
            if (_columnIndex > 1 && _columnIndex < columns.length - 1) {
              return `${this.sumNum2(data, column.property, 'planStartFlow')} ~ ${this.sumNum2(data, column.property, 'planEndFlow')}`
            }
            return null
          }),
          columns.map((column, _columnIndex) => {
            if (_columnIndex === 0) {
              return '备注'
            }
            if (_columnIndex > 1 && _columnIndex < columns.length - 1) {
              return this.remarks
            }
            return null
          }),
        ]
        return footerData
      },
      dealColumns(midTableData) {
        const dateRange = [moment(this.form.dateRange[0]), moment(this.form.dateRange[1])]

        this.tableData = midTableData
        this.columns = [
          {
            title: '所名',
            field: 'deptName',
            align: 'center',
            width: 60,
            fixed: 'left',
            headerClassName: 'custmer-span',
            slots: {
              default: () => (
                <div style='writing-mode: vertical-rl;width: 100%; display: flex; align-items: center; letter-spacing: 2px'>
                  {this.deptName}
                </div>
              ),
            },
          },
          {
            title: '',
            field: 'deptName',
            align: 'center',
            minWidth: 120,
            fixed: 'left',
            headerClassName: 'hidden-cell',
            showOverflow: true,
          },

          // 生成日期列
          ...(this.form.planType === 3
            ? // 月份模式：按月生成列
              [...Array(dateRange[1].diff(dateRange[0], 'month') + 1)].map((item, index) => {
                const date = moment(dateRange[0]).add(index, 'month').format('YYYY-MM-DD')

                return {
                  title: moment(dateRange[0]).add(index, 'month').format('YYYY年MM月'),
                  align: 'center',
                  minWidth: 120,
                  field: moment(dateRange[0]).add(index, 'month').format('YYYY-MM-DD'),
              })
            : // 日期模式：按天生成列
              [...Array(dateRange[1].diff(dateRange[0], 'day') + 1)].map((item, index) => {
                const date = moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD')

                return {
                  title: moment(dateRange[0]).add(index, 'day').format('MM月DD日'),
                  align: 'center',
                  minWidth: 120,
                  field: moment(dateRange[0]).add(index, 'day').format('YYYY-MM-DD'),
              slots: {
                default: ({ row, rowIndex }) => {
                  return (
                    <div class='table-cell-box'>
                      <a-input-number
                        size='small'
                        v-model={this.tableData[rowIndex].recordObj[date].planStartFlow}
                        min={0}
                        precision={1}
                        disabled={this.isDetail || this.form.reportType === 1}
                        onChange={val => {
                          this.$nextTick(() => {
                            const obj = this.tableData[rowIndex].recordObj[date]
                            if (obj.planStartFlow > obj.planEndFlow) {
                              this.tableData[rowIndex].recordObj[date].planEndFlow = obj.planStartFlow
                            }
                          })
                        }}
                      />
                      &nbsp;~&nbsp;
                      <a-input-number
                        size='small'
                        v-model={this.tableData[rowIndex].recordObj[date].planEndFlow}
                        min={0}
                        precision={1}
                        disabled={this.isDetail || this.form.reportType === 1}
                        onChange={val => {
                          this.$nextTick(() => {
                            const obj = this.tableData[rowIndex].recordObj[date]
                            if (obj.planStartFlow > obj.planEndFlow) {
                              this.tableData[rowIndex].recordObj[date].planStartFlow = obj.planEndFlow
                            }
                          })
                        }}
                      />
                    </div>
                  )
                },
                footer: rowInfo => {
                  // 合计
                  if (rowInfo.rowIndex === 0) {
                    if (rowInfo.itemIndex < 2 || rowInfo.itemIndex === rowInfo.items.length - 1)
                      return rowInfo.row[rowInfo.itemIndex]

                    const planStartFlowSum = this.tableData.reduce((acc, curr) => {
                      return acc + curr.recordObj[date].planStartFlow
                    }, 0)
                    const planEndFlowSum = this.tableData.reduce((acc, curr) => {
                      return acc + curr.recordObj[date].planEndFlow
                    }, 0)

                    return (
                      <div class='table-cell-box'>
                        <a-input-number disabled={true} size='small' value={planStartFlowSum} min={0} precision={1} />
                        &nbsp;~&nbsp;
                        <a-input-number disabled={true} size='small' value={planEndFlowSum} min={0} precision={1} />
                      </div>
                    )
                  }

                  // 上报
                  if (rowInfo.rowIndex === 1) {
                    if (rowInfo.itemIndex < 2 || rowInfo.itemIndex === rowInfo.items.length - 1)
                      return rowInfo.row[rowInfo.itemIndex]

                    return (
                      <div class='table-cell-box'>
                        <a-input-number
                          disabled={this.isDetail}
                          size='small'
                          v-model={this.summaries[rowInfo.itemIndex - 2].planStartFlow}
                          min={0}
                          precision={1}
                          onChange={val => {
                            this.$nextTick(() => {
                              if (val === null) {
                                this.summaries[rowInfo.itemIndex - 2].planStartFlow = 0
                              } else {
                                const obj = this.summaries[rowInfo.itemIndex - 2]
                                if (obj.planStartFlow > obj.planEndFlow) {
                                  this.summaries[rowInfo.itemIndex - 2].planStartFlow = obj.planEndFlow
                                }
                              }
                            })
                          }}
                        />
                        &nbsp;~&nbsp;
                        <a-input-number
                          disabled={this.isDetail}
                          size='small'
                          v-model={this.summaries[rowInfo.itemIndex - 2].planEndFlow}
                          min={0}
                          precision={1}
                          onChange={val => {
                            this.$nextTick(() => {
                              if (val === null) {
                                this.summaries[rowInfo.itemIndex - 2].planEndFlow = 0
                                this.summaries[rowInfo.itemIndex - 2].planStartFlow = 0
                              } else {
                                const obj = this.summaries[rowInfo.itemIndex - 2]
                                if (obj.planStartFlow > obj.planEndFlow) {
                                  this.summaries[rowInfo.itemIndex - 2].planEndFlow = obj.planStartFlow
                                }
                              }
                            })
                          }}
                        />
                      </div>
                    )
                  }
                  // 备注
                  if (rowInfo.rowIndex === 2) {
                    return <a-input size='small' v-model={this.remarks} disabled={this.isDetail} />
                  }
                },
              },
            }
          })),

          {
            title: '备注',
            field: 'remark',
            align: 'center',
            minWidth: 120,
            showOverflow: true,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <a-input
                    size='small'
                    v-model={this.tableData[rowIndex].remarks}
                    disabled={this.isDetail || this.form.reportType === 1}
                  />
                )
              },
            },
          },
        ]
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 修改按钮操作 */
      handleAdd(row) {
        this.open = true
        this.formTitle = '用水计划上报'
        this.modalLoading = true
        this.rowInfo = row ? { ...row } : null
        this.isDetail = row?.isDetail || false

        if (!this.rowInfo) {
          getOrgTree().then(res => {
            const userDeptId = JSON.parse(localStorage.getItem('user')).deptId
            const flatObj = getMapFlatTree(res.data, 'deptId')

            this.deptName = flatObj?.[userDeptId]?.deptName

            this.handlePlanTypeChange({ target: { value: 1 } })
          })
        } else {
          this.deptName = this.rowInfo.deptName
          this.form.planReportId = this.rowInfo.planReportId
          this.handlePlanTypeChange({ target: { value: 1 } })
        }
      },
      getPlanInfo() {
        const dealFn = res => {
          const midTableData = res.data.map(item => {
            const recordObj = {}
            item.records.forEach(item => {
              recordObj[item.planDate] = {
                planDate: item.planDate,
                planStartFlow: item.planStartFlow,
                planEndFlow: item.planEndFlow,
              }
            })
            return {
              ...item,
              recordObj,
            }
          })

          // 处理总计
          if (!this.rowInfo) {
            const arr = []
            midTableData[0].records.forEach(el => {
              arr.push(el)
            })
            arr.forEach(ele => {
              ele.planStartFlow = midTableData.reduce((accumulator, currentValue) => {
                return accumulator + Number(currentValue.recordObj[ele.planDate].planStartFlow)
              }, 0)
              ele.planEndFlow = midTableData.reduce((accumulator, currentValue) => {
                return accumulator + Number(currentValue.recordObj[ele.planDate].planEndFlow)
              }, 0)
            })
            this.summaries = arr
          }

          this.dealColumns(midTableData)

          // // 合并表头
          // this.$nextTick(() => {
          //   setTimeout(() => {
          //     this.$nextTick(() => {
          //       document.querySelectorAll('.custmer-span').forEach(item => {
          //         item.setAttribute('colspan', '2')
          //       })
          //     })
          //   }, 400)
          // })

          this.modalLoading = false
        }

        this.tableData = []
        this.columns = []

        this.$nextTick(() => {
          if (this.rowInfo) {
            getPlanReportDetails({ planReportId: this.rowInfo.planReportId }).then(resp => {
              this.summaries = resp.data.summaries
              this.remarks = resp.data.remarks

              this.form.reportType = resp.data.reportType

              dealFn({ data: resp.data.reportDetailsVOs || [] })
            })
          } else {
            getPlanReportList({
              reportType: this.form.reportType,
              planType: this.form.planType,
              planStartDate: this.form.dateRange[0].format('YYYY-MM-DD'),
              planEndDate: this.form.dateRange[1].format('YYYY-MM-DD'),
            }).then(res => {
              dealFn(res)
            })
          }
        })
      },

      /** 提交按钮 */
      submitForm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            const params = {
              ...this.form,
              dateRange: undefined,
              planStartDate: this.form.dateRange[0].format('YYYY-MM-DD'),
              planEndDate: this.form.dateRange[1].format('YYYY-MM-DD'),
              remarks: this.remarks,
              summaries: this.summaries,
              planWaters: this.tableData.map(ele => {
                return {
                  ...ele,
                  records: Object.values(ele.recordObj).map(item => {
                    if (item.planStartFlow === undefined && item.planEndFlow === undefined) {
                      return { ...item, planStartFlow: 0, planEndFlow: 0 }
                    }
                    if (item.planStartFlow === undefined || item.planEndFlow === undefined) {
                      if (item.planStartFlow === undefined) {
                        return { ...item, planStartFlow: item.planEndFlow }
                      }
                      if (item.planEndFlow === undefined) {
                        return { ...item, planEndFlow: item.planStartFlow }
                      }
                    }

                    return item
                  }),
                }
              }),
              reportDetailsVOs: this.tableData.map(ele => {
                return {
                  ...ele,
                  records: Object.values(ele.recordObj).map(item => {
                    if (item.planStartFlow === undefined && item.planEndFlow === undefined) {
                      return { ...item, planStartFlow: 0, planEndFlow: 0 }
                    }
                    if (item.planStartFlow === undefined || item.planEndFlow === undefined) {
                      if (item.planStartFlow === undefined) {
                        return { ...item, planStartFlow: item.planEndFlow }
                      }
                      if (item.planEndFlow === undefined) {
                        return { ...item, planEndFlow: item.planStartFlow }
                      }
                    }

                    return item
                  }),
                }
              }),
            }

            this.loading = true

            if (this.rowInfo) {
              updatePlanReport(params)
                .then(response => {
                  this.$message.success('修改成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                })
                .finally(() => (this.loading = false))
            } else {
              addPlanReport(params)
                .then(response => {
                  this.$message.success('新增成功', 3)
                  this.open = false
                  this.$emit('close')
                  this.$emit('ok')
                })
                .finally(() => (this.loading = false))
            }
          } else {
            return false
          }
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  ::v-deep .table-cell-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .ant-input-number-handler-wrap {
      display: none;
    }
  }

  ::v-deep .modal-content {
    height: 100%;
  }

  // ::v-deep .hidden-cell {
  //   display: none !important;
  // }
</style>
